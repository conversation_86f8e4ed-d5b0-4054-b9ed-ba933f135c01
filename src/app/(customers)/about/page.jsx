'use client';

import { Card, CardDescription, CardHeader, Card<PERSON><PERSON>le, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Heart, Target, Users, Zap, Award, Globe, Rocket, Star, Quote } from "lucide-react"
import { motion } from "motion/react";
import { Newsletter } from "@/components/sections/Newsletter"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import Link from "next/link"

export default function AboutPage() {
  const values = [
    {
      icon: Heart,
      title: "Passion for Quality",
      description: "We believe in creating and sharing content that truly matters and adds value to our readers' lives.",
      color: "text-red-500"
    },
    {
      icon: Users,
      title: "Community First",
      description: "Our platform is built around fostering meaningful connections between writers and readers.",
      color: "text-blue-500"
    },
    {
      icon: Target,
      title: "Purpose Driven",
      description: "Every article we publish serves a purpose - to educate, inspire, or entertain our community.",
      color: "text-green-500"
    },
    {
      icon: Zap,
      title: "Innovation",
      description: "We continuously evolve our platform to provide the best reading and writing experience.",
      color: "text-purple-500"
    },
  ]

  const timeline = [
    {
      year: "2024",
      title: "The Beginning",
      description: "TechCulture was founded with a vision to democratize tech knowledge and create a platform where innovation meets accessibility.",
      icon: Rocket,
      color: "bg-blue-500"
    },
    {
      year: "2024",
      title: "Community Growth",
      description: "Reached 10,000 active readers and published over 500 high-quality articles across various tech domains.",
      icon: Users,
      color: "bg-green-500"
    },
    {
      year: "2024",
      title: "Global Recognition",
      description: "Featured in major tech publications and recognized as one of the fastest-growing tech blogs.",
      icon: Award,
      color: "bg-purple-500"
    },
    {
      year: "Future",
      title: "What's Next",
      description: "Expanding into video content, podcasts, and interactive learning experiences for our community.",
      icon: Globe,
      color: "bg-orange-500"
    }
  ]

  const team = [
    {
      name: "Sarah Johnson",
      role: "Founder & Editor-in-Chief",
      bio: "Former tech journalist with 10+ years of experience in digital publishing. Passionate about making complex technology accessible to everyone.",
      image: "https://plus.unsplash.com/premium_photo-1690407617542-2f210cf20d7e?q=80&w=100&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
      expertise: ["Tech Journalism", "Content Strategy", "Digital Publishing"],
      social: {
        twitter: "@sarahjohnson",
        linkedin: "sarah-johnson-tech"
      }
    },
    {
      name: "Michael Chen",
      role: "Head of Technology",
      bio: "Full-stack developer passionate about creating seamless user experiences. Specializes in modern web technologies and scalable architectures.",
      image: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=300&h=300&fit=crop&crop=face",
      expertise: ["Full-Stack Development", "Cloud Architecture", "DevOps"],
      social: {
        twitter: "@michaelchen",
        linkedin: "michael-chen-dev"
      }
    },
    {
      name: "Emily Rodriguez",
      role: "Community Manager",
      bio: "Expert in building and nurturing online communities. Believes in the power of authentic connections and meaningful conversations.",
      image: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=300&h=300&fit=crop&crop=face",
      expertise: ["Community Building", "Social Media", "Content Marketing"],
      social: {
        twitter: "@emilyrodriguez",
        linkedin: "emily-rodriguez-community"
      }
    },
    {
      name: "David Kim",
      role: "Senior Writer & AI Specialist",
      bio: "AI researcher turned writer, making artificial intelligence concepts accessible to mainstream audiences through compelling storytelling.",
      image: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=300&h=300&fit=crop&crop=face",
      expertise: ["Artificial Intelligence", "Machine Learning", "Technical Writing"],
      social: {
        twitter: "@davidkim",
        linkedin: "david-kim-ai"
      }
    }
  ]

  return (
    <div className="flex flex-col">
      {/* Hero Section */}
      <motion.section
        initial={{ opacity: 0, y: 50 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8 }}
        className="section-modern bg-gradient-to-br from-primary/5 via-background to-primary/10"
      >
        <div className="container-modern">
          <div className="max-w-5xl mx-auto text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="mb-8"
            >
              <Badge variant="outline" className="px-4 py-2 text-sm mb-6">
                Our Story
              </Badge>
              <h1 className="text-display mb-8 text-shimmer">
                About <span className="text-gradient-primary">TechCulture</span>
              </h1>
              <p className="text-xl text-muted-foreground mb-8 max-w-4xl mx-auto leading-relaxed">
                We're on a mission to democratize technology knowledge and create the world's most engaging platform
                for sharing insights, stories, and innovations. Founded in 2024, TechCulture has grown into a thriving
                community of writers and readers who believe in the power of accessible, quality content.
              </p>
            </motion.div>

            {/* Hero stats */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              className="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-3xl mx-auto"
            >
              {[
                { value: "50K+", label: "Active Readers" },
                { value: "500+", label: "Articles Published" },
                { value: "100+", label: "Expert Writers" },
                { value: "25+", label: "Tech Categories" }
              ].map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="text-3xl font-bold text-gradient-primary mb-2">{stat.value}</div>
                  <div className="text-sm text-muted-foreground">{stat.label}</div>
                </div>
              ))}
            </motion.div>
          </div>
        </div>
      </motion.section>

      {/* Mission Section */}
      <motion.section
        initial={{ opacity: 0, y: 50 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        viewport={{ once: true }}
        className="section-modern bg-muted/30"
      >
        <div className="container-modern">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <motion.h2
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
                viewport={{ once: true }}
                className="text-headline mb-4"
              >
                Our Mission
              </motion.h2>
              <motion.p
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.1 }}
                viewport={{ once: true }}
                className="text-muted-foreground text-lg"
              >
                Empowering voices and connecting minds through exceptional content
              </motion.p>
            </div>

            <div className="grid lg:grid-cols-2 gap-16 items-center">
              <motion.div
                initial={{ opacity: 0, x: -50 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6 }}
                viewport={{ once: true }}
              >
                <h3 className="text-2xl font-bold mb-6">What We Believe</h3>
                <div className="space-y-6">
                  <div className="flex gap-4">
                    <div className="w-12 h-12 bg-gradient-to-br from-primary/20 to-primary/10 rounded-xl flex items-center justify-center flex-shrink-0">
                      <Quote className="w-6 h-6 text-primary" />
                    </div>
                    <div>
                      <h4 className="font-semibold mb-2">Democratizing Knowledge</h4>
                      <p className="text-muted-foreground">
                        Every person has unique insights to share. We provide the platform and community to amplify these voices.
                      </p>
                    </div>
                  </div>
                  <div className="flex gap-4">
                    <div className="w-12 h-12 bg-gradient-to-br from-green-500/20 to-green-500/10 rounded-xl flex items-center justify-center flex-shrink-0">
                      <Users className="w-6 h-6 text-green-500" />
                    </div>
                    <div>
                      <h4 className="font-semibold mb-2">Building Community</h4>
                      <p className="text-muted-foreground">
                        We foster meaningful connections between writers and readers, creating a space for authentic dialogue.
                      </p>
                    </div>
                  </div>
                  <div className="flex gap-4">
                    <div className="w-12 h-12 bg-gradient-to-br from-purple-500/20 to-purple-500/10 rounded-xl flex items-center justify-center flex-shrink-0">
                      <Star className="w-6 h-6 text-purple-500" />
                    </div>
                    <div>
                      <h4 className="font-semibold mb-2">Quality First</h4>
                      <p className="text-muted-foreground">
                        We maintain the highest standards while fostering an inclusive environment where diverse perspectives thrive.
                      </p>
                    </div>
                  </div>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: 50 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6 }}
                viewport={{ once: true }}
                className="relative"
              >
                <div className="aspect-square bg-gradient-to-br from-primary/10 to-primary/5 rounded-3xl overflow-hidden">
                  <img
                    src="https://images.unsplash.com/photo-1522071820081-009f0129c71c?w=600&h=600&fit=crop"
                    alt="Our mission - team collaboration"
                    className="w-full h-full object-cover"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-primary/20 to-transparent" />
                </div>
                {/* Floating elements */}
                <div className="absolute -top-4 -right-4 bg-background rounded-2xl p-4 shadow-xl border">
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                    <span className="text-sm font-medium">50K+ Readers</span>
                  </div>
                </div>
                <div className="absolute -bottom-4 -left-4 bg-background rounded-2xl p-4 shadow-xl border">
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                    <span className="text-sm font-medium">500+ Articles</span>
                  </div>
                </div>
              </motion.div>
            </div>
          </div>
        </div>
      </motion.section>

      {/* Timeline Section */}
      <motion.section
        initial={{ opacity: 0, y: 50 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        viewport={{ once: true }}
        className="section-modern"
      >
        <div className="container-modern">
          <div className="text-center mb-16">
            <motion.h2
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
              className="text-headline mb-4"
            >
              Our Journey
            </motion.h2>
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
              viewport={{ once: true }}
              className="text-muted-foreground text-lg"
            >
              From a simple idea to a thriving community
            </motion.p>
          </div>

          <div className="max-w-4xl mx-auto">
            <div className="relative">
              {/* Timeline line */}
              <div className="absolute left-8 top-0 bottom-0 w-0.5 bg-gradient-to-b from-primary via-primary/50 to-primary/20"></div>

              <div className="space-y-12">
                {timeline.map((item, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, x: -50 }}
                    whileInView={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                    viewport={{ once: true }}
                    className="relative flex items-start gap-8"
                  >
                    {/* Timeline dot */}
                    <div className={`w-16 h-16 ${item.color} rounded-2xl flex items-center justify-center text-white shadow-lg z-10`}>
                      <item.icon className="w-8 h-8" />
                    </div>

                    {/* Content */}
                    <div className="flex-1 pb-8">
                      <div className="flex items-center gap-3 mb-3">
                        <Badge variant="outline" className="text-xs">
                          {item.year}
                        </Badge>
                        <h3 className="text-xl font-bold">{item.title}</h3>
                      </div>
                      <p className="text-muted-foreground leading-relaxed">
                        {item.description}
                      </p>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </motion.section>

      {/* Values Section */}
      <motion.section
        initial={{ opacity: 0, y: 50 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        viewport={{ once: true }}
        className="section-modern bg-gradient-to-br from-muted/30 via-background to-muted/20"
      >
        <div className="container-modern">
          <div className="text-center mb-16">
            <motion.h2
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
              className="text-headline mb-4"
            >
              Our Values
            </motion.h2>
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
              viewport={{ once: true }}
              className="text-muted-foreground text-lg"
            >
              The principles that guide everything we do
            </motion.p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {values.map((value, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                whileHover={{ y: -8 }}
                className="group"
              >
                <Card className="text-center h-full border-0 shadow-lg hover:shadow-2xl transition-all duration-500 bg-gradient-to-br from-card to-card/80 backdrop-blur-sm">
                  <CardHeader className="pb-6">
                    <div className={`mx-auto w-16 h-16 bg-gradient-to-br from-primary/20 to-primary/10 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300`}>
                      <value.icon className={`h-8 w-8 ${value.color}`} />
                    </div>
                    <CardTitle className="text-xl mb-3 group-hover:text-primary transition-colors duration-300">
                      {value.title}
                    </CardTitle>
                    <CardDescription className="leading-relaxed">
                      {value.description}
                    </CardDescription>
                  </CardHeader>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </motion.section>

      {/* Team Section */}
      <motion.section
        initial={{ opacity: 0, y: 50 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        viewport={{ once: true }}
        className="section-modern bg-muted/30"
      >
        <div className="container-modern">
          <div className="text-center mb-16">
            <motion.h2
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
              className="text-headline mb-4"
            >
              Meet Our Team
            </motion.h2>
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
              viewport={{ once: true }}
              className="text-muted-foreground text-lg"
            >
              The passionate people behind TechCulture
            </motion.p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {team.map((member, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                whileHover={{ y: -8 }}
                className="group"
              >
                <Card className="text-center h-full border-0 shadow-lg hover:shadow-2xl transition-all duration-500 bg-gradient-to-br from-card to-card/80 backdrop-blur-sm overflow-hidden">
                  <div className="relative">
                    <div className="h-32 bg-gradient-to-br from-primary/10 to-primary/5"></div>
                    <div className="absolute -bottom-12 left-1/2 transform -translate-x-1/2">
                      <Avatar className="w-24 h-24 ring-4 ring-background group-hover:ring-primary/20 transition-all duration-300">
                        <AvatarImage src={member.image} alt={member.name} />
                        <AvatarFallback className="text-lg">
                          {member.name.split(' ').map(n => n[0]).join('')}
                        </AvatarFallback>
                      </Avatar>
                    </div>
                  </div>

                  <CardContent className="pt-16 pb-6">
                    <h3 className="text-xl font-bold mb-2 group-hover:text-primary transition-colors duration-300">
                      {member.name}
                    </h3>
                    <Badge variant="secondary" className="mb-4">
                      {member.role}
                    </Badge>
                    <p className="text-muted-foreground text-sm leading-relaxed mb-4">
                      {member.bio}
                    </p>

                    {/* Expertise tags */}
                    <div className="flex flex-wrap gap-1 justify-center mb-4">
                      {member.expertise.slice(0, 2).map((skill, skillIndex) => (
                        <Badge key={skillIndex} variant="outline" className="text-xs">
                          {skill}
                        </Badge>
                      ))}
                    </div>

                    {/* Social links */}
                    <div className="flex justify-center gap-3">
                      <Link href={`https://twitter.com/${member.social.twitter}`} target="_blank">
                        <Button variant="ghost" size="sm" className="p-2 hover:bg-blue-500 hover:text-white transition-all duration-300">
                          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z" />
                          </svg>
                        </Button>
                      </Link>
                      <Link href={`https://linkedin.com/in/${member.social.linkedin}`} target="_blank">
                        <Button variant="ghost" size="sm" className="p-2 hover:bg-blue-700 hover:text-white transition-all duration-300">
                          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z" />
                          </svg>
                        </Button>
                      </Link>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </motion.section>

      {/* Newsletter Section */}
      <Newsletter />
    </div>
  )
};
