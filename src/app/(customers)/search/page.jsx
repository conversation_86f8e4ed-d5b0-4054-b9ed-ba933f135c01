export const metadata = {
  title: `Search Articles | ${CompanyName}`,
  description: 'Search and discover articles across all categories. Find content by keywords, authors, topics, and more.',
  keywords: 'search, articles, blog search, find content, filter articles, search by author, search by category',
  openGraph: {
    title: `Search Articles | ${CompanyName}`,
    description: 'Search and discover articles across all categories. Find content by keywords, authors, topics, and more.',
    type: 'website',
  },
};

import { StructuredData } from '@/components/seo/StructuredData';
import { SearchPageClient } from '@/components/sections/SearchPageClient';
import { CompanyName } from '@/constants/companyName';

export default function SearchPage() {
  return (
    <>
      <StructuredData
        type="breadcrumb"
        data={{
          breadcrumbs: [
            { name: "Home", url: "/home" },
            { name: "Search", url: "/search" }
          ]
        }}
      />
      <SearchPageClient />
    </>
  );
}
