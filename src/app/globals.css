@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-ring: var(--ring);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);

  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);

  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.65rem;

  /* Light Mode Colors */
  --background: oklch(1 0 0);
  --foreground: oklch(0.141 0.005 285.823);
  --card: var(--background);
  --card-foreground: var(--foreground);
  --popover: var(--background);
  --popover-foreground: var(--foreground);

  --primary: oklch(0.5821 0.0948 203.26);
  --primary-foreground: oklch(0.97 0.014 254.604);
  --secondary: oklch(0.967 0.001 286.375);
  --secondary-foreground: oklch(0.21 0.006 285.885);
  --muted: var(--secondary);
  --muted-foreground: oklch(0.552 0.016 285.938);
  --accent: var(--secondary);
  --accent-foreground: var(--secondary-foreground);
  --destructive: oklch(0.577 0.245 27.325);

  --border: oklch(0.92 0.004 286.32);
  --input: var(--border);
  --ring: var(--primary);

  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);

  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: var(--foreground);
  --sidebar-primary: oklch(0.623 0.214 259.815);
  --sidebar-primary-foreground: var(--primary-foreground);
  --sidebar-accent: var(--accent);
  --sidebar-accent-foreground: var(--accent-foreground);
  --sidebar-border: var(--border);
  --sidebar-ring: var(--ring);
}

.dark {
  /* Dark Mode Overrides */
  --background: oklch(0.141 0.005 285.823);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.21 0.006 285.885);
  --card-foreground: var(--foreground);
  --popover: var(--card);
  --popover-foreground: var(--foreground);
  --primary-foreground: oklch(0.379 0.146 265.522);
  --secondary: oklch(0.274 0.006 286.033);
  --secondary-foreground: oklch(0.967 0.001 286.375);
  --muted: var(--secondary);
  --muted-foreground: oklch(0.705 0.015 286.067);
  --accent: var(--secondary);
  --accent-foreground: var(--foreground);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: var(--card);
  --sidebar-foreground: var(--foreground);
  --sidebar-primary: oklch(0.546 0.245 262.881);
  --sidebar-primary-foreground: var(--primary-foreground);
  --sidebar-accent: var(--secondary);
  --sidebar-accent-foreground: var(--foreground);
  --sidebar-border: var(--border);
  --sidebar-ring: var(--ring);
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  html {
    scroll-behavior: smooth;
  }

  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-muted;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-muted-foreground/30 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-muted-foreground/50;
  }

  /* Hide scrollbar utility */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  ::selection {
    @apply bg-primary/20 text-primary-foreground;
  }

  :focus-visible {
    @apply outline-2 outline-offset-2 outline-ring;
  }

  :focus {
    outline: none;
  }

  button:focus-visible,
  input:focus-visible,
  textarea:focus-visible,
  select:focus-visible,
  a:focus-visible,
  [tabindex]:focus-visible {
    @apply outline-2 outline-offset-2 outline-ring;
  }
}

@layer components {

  /* Gradients & Effects */
  .text-gradient-primary {
    @apply bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent;
  }

  .glass {
    @apply bg-background/80 backdrop-blur-xl border border-border/50;
  }

  .card-hover {
    @apply transition-all duration-300 hover:shadow-xl hover:shadow-primary/5 hover:-translate-y-1;
  }

  .btn-modern {
    @apply relative overflow-hidden transition-all duration-300;
  }

  .btn-modern::before {
    content: '';
    @apply absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent translate-x-[-100%] transition-transform duration-700;
  }

  .btn-modern:hover::before {
    @apply translate-x-[100%];
  }

  .text-shimmer {
    @apply bg-gradient-to-r from-foreground via-primary to-foreground bg-[length:200%_100%] bg-clip-text text-transparent;
    animation: shimmer 3s ease-in-out infinite;
  }

  @keyframes shimmer {

    0%,
    100% {
      background-position: 200% 0;
    }

    50% {
      background-position: -200% 0;
    }
  }

  .pulse-modern {
    animation: pulse-modern 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  @keyframes pulse-modern {

    0%,
    100% {
      opacity: 1;
    }

    50% {
      opacity: 0.7;
    }
  }

  /* Layout */
  .bg-footer-gradient {
    background: linear-gradient(135deg, #2C3E50 0%, #1F1F1F 100%);
  }

  .container-modern {
    @apply mx-auto px-4 sm:px-6 lg:px-8;
    max-width: min(1400px, calc(100vw - 2rem));
  }

  .section-modern {
    @apply mt-20 py-10;
  }

  .space-y-modern>*+* {
    @apply mt-6 md:mt-8 lg:mt-12;
  }

  /* Typography */
  .text-display {
    @apply text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-bold tracking-tight;
  }

  .text-headline {
    @apply text-2xl md:text-3xl lg:text-4xl font-semibold tracking-tight;
  }

  .text-subheadline {
    @apply text-lg md:text-xl lg:text-2xl font-medium;
  }

  .responsive-text {
    font-size: clamp(1rem, 2.5vw, 1.5rem);
  }

  .responsive-heading {
    font-size: clamp(1.5rem, 5vw, 3rem);
  }

  .responsive-display {
    font-size: clamp(2rem, 8vw, 5rem);
  }

  /* Optimizations */
  .will-change-transform {
    will-change: transform;
  }

  .will-change-opacity {
    will-change: opacity;
  }

  .gpu-accelerated {
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
  }

  /* Accessibility */
  .focus-trap {
    position: relative;
  }

  .focus-trap::before,
  .focus-trap::after {
    content: '';
    position: absolute;
    width: 1px;
    height: 1px;
    opacity: 0;
    pointer-events: none;
  }

  /* Animations & Loading */
  .loading-skeleton {
    background: linear-gradient(90deg, var(--muted) 25%, var(--muted-foreground) 50%, var(--muted) 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
  }

  @keyframes skeleton-loading {
    0% {
      background-position: 200% 0;
    }

    100% {
      background-position: -200% 0;
    }
  }

  .lazy-load {
    opacity: 0;
    transition: opacity 0.3s;
  }

  .lazy-load.loaded {
    opacity: 1;
  }

  /* Touch and Scroll */
  .touch-action-manipulation {
    touch-action: manipulation;
  }

  .touch-action-pan-y {
    touch-action: pan-y;
  }

  .scroll-snap-x {
    scroll-snap-type: x mandatory;
  }

  .scroll-snap-y {
    scroll-snap-type: y mandatory;
  }

  .scroll-snap-start {
    scroll-snap-align: start;
  }

  .scroll-snap-center {
    scroll-snap-align: center;
  }

  /* Media Queries */
  @media print {
    .no-print {
      display: none !important;
    }

    .print-only {
      display: block !important;
    }

    * {
      background: transparent !important;
      color: black !important;
      box-shadow: none !important;
      text-shadow: none !important;
    }

    a[href]::after {
      content: " (" attr(href) ")";
    }

    abbr[title]::after {
      content: " (" attr(title) ")";
    }

    .page-break {
      page-break-before: always;
    }
  }

  @media (prefers-contrast: high) {
    .auto-contrast {
      filter: contrast(150%);
    }
  }

  @media (prefers-reduced-data: reduce) {

    .data-heavy,
    video {
      display: none;
    }

    .background-image {
      background-image: none !important;
    }
  }

  @container (min-width: 400px) {
    .container-responsive {
      padding: 2rem;
    }
  }

  @container (min-width: 600px) {
    .container-responsive {
      padding: 3rem;
    }
  }
}
