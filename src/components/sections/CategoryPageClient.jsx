'use client';

import { useState } from 'react';
import { motion } from 'motion/react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Sheet, SheetContent, SheetHeader, SheetTitle, SheetTrigger } from '@/components/ui/sheet';
import { useIsMobile } from '@/components/ui/use-mobile';
import {
  ArrowLeft,
  TrendingUp,
  Filter,
  Search,
  Clock,
  Eye,
  SlidersHorizontal,
  ArrowUpDown
} from 'lucide-react';
import Link from 'next/link';
import { BLOGS } from '@/constants/blogs';

export function CategoryPageClient({ categoryData, slug }) {
  const isMobile = useIsMobile();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedTags, setSelectedTags] = useState([]);
  const [selectedFormat, setSelectedFormat] = useState('All');
  const [selectedAuthor, setSelectedAuthor] = useState('All Authors');
  const [isMobileFilterOpen, setIsMobileFilterOpen] = useState(false);
  
  const categoryBlogs = BLOGS.filter(blog => 
    blog.category.toLowerCase() === slug?.toLowerCase()
  );

  // Get unique authors from category blogs
  const categoryAuthors = [...new Set(categoryBlogs.map(blog => `${blog.author.first_name} ${blog.author.last_name}`))];
  
  // Available tags for this category
  const availableTags = categoryData?.topics || [];

  // Filter blogs based on search and filters
  const filteredBlogs = categoryBlogs.filter(blog => {
    const authorName = `${blog.author.first_name} ${blog.author.last_name}`;
    const matchesSearch = searchQuery === '' || 
      blog.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      blog.description.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesTags = selectedTags.length === 0 || 
      selectedTags.some(tag => blog.tags?.includes(tag));
    
    const matchesAuthor = selectedAuthor === 'All Authors' || authorName === selectedAuthor;
    
    return matchesSearch && matchesTags && matchesAuthor;
  });

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="border-b bg-background/95 backdrop-blur-sm sticky top-16 md:top-20 z-40">
        <div className="container-modern py-4 md:py-6">
          <div className="flex items-center gap-3 mb-4 md:mb-6">
            <Link href="/categories" className="flex items-center text-muted-foreground hover:text-foreground transition-colors">
              <ArrowLeft className="w-4 h-4 mr-2" />
              <span className="hidden sm:inline">Back to Categories</span>
              <span className="sm:hidden">Back</span>
            </Link>
            <span className="text-muted-foreground">•</span>
            <span className="text-sm text-muted-foreground">{filteredBlogs.length} articles</span>
          </div>

          {/* Category Header Card */}
          <div className="bg-gradient-to-r from-primary/10 to-primary/5 rounded-xl p-4 md:p-6 mb-4 md:mb-6">
            <div className="flex items-start gap-4">
              <div className="w-10 h-10 md:w-12 md:h-12 bg-primary rounded-lg flex items-center justify-center text-primary-foreground text-lg md:text-xl font-bold">
                {categoryData.icon}
              </div>
              <div className="flex-1">
                <h1 className="text-xl md:text-2xl font-bold mb-2">{categoryData.title}</h1>
                <p className="text-muted-foreground text-sm md:text-base mb-4">{categoryData.description}</p>
                <div className="flex items-center gap-4 md:gap-6 text-xs md:text-sm text-muted-foreground">
                  <span>{categoryData.stats.articles} articles</span>
                  <span>{categoryData.stats.readers} followers</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile Filter/Sort Bar */}
      {isMobile && (
        <div className="border-b bg-background/95 backdrop-blur-sm sticky top-[140px] z-30">
          <div className="container-modern py-3">
            <div className="flex items-center justify-between gap-3">
              <div className="flex items-center gap-2">
                <Sheet open={isMobileFilterOpen} onOpenChange={setIsMobileFilterOpen}>
                  <SheetTrigger asChild>
                    <Button variant="outline" size="sm" className="flex items-center gap-2">
                      <Filter className="w-4 h-4" />
                      <span>Filter</span>
                      {(selectedTags.length > 0 || selectedAuthor !== 'All Authors' || searchQuery) && (
                        <span className="bg-primary text-primary-foreground text-xs rounded-full w-5 h-5 flex items-center justify-center">
                          {selectedTags.length + (selectedAuthor !== 'All Authors' ? 1 : 0) + (searchQuery ? 1 : 0)}
                        </span>
                      )}
                    </Button>
                  </SheetTrigger>
                  <SheetContent side="left" className="w-full sm:w-80 p-0 flex flex-col">
                    <SheetHeader className="p-4 border-b">
                      <SheetTitle className="flex items-center gap-2">
                        <SlidersHorizontal className="w-5 h-5" />
                        Filters
                      </SheetTitle>
                    </SheetHeader>
                    <div className="flex-1 overflow-y-auto p-4">
                      {/* Search */}
                      <div className="mb-6">
                        <h3 className="font-medium mb-3">Search</h3>
                        <div className="relative">
                          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
                          <Input
                            placeholder="Search articles..."
                            value={searchQuery}
                            onChange={(e) => setSearchQuery(e.target.value)}
                            className="pl-10"
                          />
                        </div>
                      </div>

                      {/* Tags */}
                      <div className="mb-6">
                        <h3 className="font-medium mb-3">Tags</h3>
                        <div className="flex flex-wrap gap-2">
                          {availableTags.slice(0, 8).map((tag) => (
                            <button
                              key={tag}
                              onClick={() => {
                                setSelectedTags(prev =>
                                  prev.includes(tag)
                                    ? prev.filter(t => t !== tag)
                                    : [...prev, tag]
                                );
                              }}
                              className={`px-3 py-1 text-xs rounded-full border transition-colors ${
                                selectedTags.includes(tag)
                                  ? 'bg-primary text-primary-foreground border-primary'
                                  : 'bg-muted text-muted-foreground border-border hover:border-primary/50'
                              }`}
                            >
                              {tag}
                            </button>
                          ))}
                        </div>
                      </div>

                      {/* Author Filter */}
                      <div className="mb-6">
                        <h3 className="font-medium mb-3">Author</h3>
                        <Select value={selectedAuthor} onValueChange={setSelectedAuthor}>
                          <SelectTrigger className="w-full">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="All Authors">All Authors</SelectItem>
                            {categoryAuthors.map((author) => (
                              <SelectItem key={author} value={author}>{author}</SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                    <div className="p-4 border-t bg-background">
                      <div className="flex gap-2">
                        <Button
                          variant="outline"
                          className="flex-1"
                          onClick={() => setIsMobileFilterOpen(false)}
                        >
                          Close
                        </Button>
                        <Button
                          className="flex-1"
                          onClick={() => setIsMobileFilterOpen(false)}
                        >
                          Show {filteredBlogs.length} Result{filteredBlogs.length !== 1 ? 's' : ''}
                        </Button>
                      </div>
                    </div>
                  </SheetContent>
                </Sheet>
              </div>

              <div className="text-sm text-muted-foreground">
                {filteredBlogs.length} result{filteredBlogs.length !== 1 ? 's' : ''}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Main Content */}
      <div className="container-modern py-4 md:py-6">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-4 md:gap-6">
          {/* Left Sidebar - Filters - Hidden on mobile */}
          {!isMobile && (
            <div className="lg:col-span-1">
              <div className="bg-card rounded-lg border p-6 sticky top-32">
              {/* Filter & Search Header */}
              <div className="flex items-center gap-2 mb-6">
                <Filter className="w-4 h-4 text-muted-foreground" />
                <span className="font-medium">Filter & Search</span>
              </div>

              {/* Search */}
              <div className="mb-6">
                <div className="relative">
                  <Search className="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground" />
                  <Input
                    placeholder="Search within this category"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>

              {/* Tags */}
              <div className="mb-6">
                <h3 className="font-medium mb-3">TAGS</h3>
                <div className="flex flex-wrap gap-2">
                  {availableTags.slice(0, 8).map((tag) => (
                    <button
                      key={tag}
                      onClick={() => {
                        setSelectedTags(prev =>
                          prev.includes(tag)
                            ? prev.filter(t => t !== tag)
                            : [...prev, tag]
                        );
                      }}
                      className={`px-3 py-1 text-xs rounded-full border transition-colors ${
                        selectedTags.includes(tag)
                          ? 'bg-primary text-primary-foreground border-primary'
                          : 'bg-muted text-muted-foreground border-border hover:border-primary/50'
                      }`}
                    >
                      {tag}
                    </button>
                  ))}
                </div>
              </div>

              {/* Format Filter */}
              <div className="mb-6">
                <h3 className="font-medium mb-3">Format</h3>
                <Select value={selectedFormat} onValueChange={setSelectedFormat}>
                  <SelectTrigger className="w-full">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="All">All</SelectItem>
                    <SelectItem value="Article">Article</SelectItem>
                    <SelectItem value="Tutorial">Tutorial</SelectItem>
                    <SelectItem value="Guide">Guide</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Author Filter */}
              <div className="mb-6">
                <h3 className="font-medium mb-3">Author</h3>
                <Select value={selectedAuthor} onValueChange={setSelectedAuthor}>
                  <SelectTrigger className="w-full">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="All Authors">All Authors</SelectItem>
                    {categoryAuthors.map((author) => (
                      <SelectItem key={author} value={author}>{author}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
            </div>
          )}

          {/* Main Content - Articles */}
          <div className={isMobile ? "col-span-1" : "lg:col-span-2"}>
            <div className="space-y-6">
              {filteredBlogs.map((blog, index) => (
                <motion.div
                  key={blog.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                >
                  <Link href={`/blog/${blog.slug}`}>
                    <Card className="overflow-hidden hover:shadow-lg transition-all duration-300 cursor-pointer group">
                      <div className={isMobile ? "flex flex-col" : "flex"}>
                        <div className={`${isMobile ? "w-full h-48" : "w-48 h-32 flex-shrink-0"} relative overflow-hidden`}>
                          <img
                            src={blog.image}
                            alt={blog.title}
                            className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                          />
                        </div>
                        <div className={isMobile ? "p-4" : "flex-1 p-6"}>
                          <div className="flex items-start justify-between mb-2">
                            <Badge variant="secondary" className="text-xs">
                              Featured
                            </Badge>
                          </div>
                          <h3 className="text-lg font-semibold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors">
                            {blog.title}
                          </h3>
                          <p className="text-gray-600 text-sm mb-4 line-clamp-2">
                            {blog.description}
                          </p>
                          <div className="flex flex-wrap gap-2 mb-4">
                            {availableTags.slice(0, 2).map((tag) => (
                              <span key={tag} className="px-2 py-1 bg-blue-50 text-blue-600 text-xs rounded">
                                {tag}
                              </span>
                            ))}
                          </div>
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-3">
                              <Avatar className="w-6 h-6">
                                <AvatarImage src={blog.author.avatar} />
                                <AvatarFallback className="text-xs">
                                  {blog.author.first_name[0]}{blog.author.last_name[0]}
                                </AvatarFallback>
                              </Avatar>
                              <span className="text-sm text-gray-600">{blog.author.first_name} {blog.author.last_name}</span>
                              <span className="text-gray-400">•</span>
                              <span className="text-sm text-gray-500">{blog.publishedAt}</span>
                            </div>
                            <div className="flex items-center gap-4 text-sm text-gray-500">
                              <span className="flex items-center gap-1">
                                <Clock className="w-3 h-3" />
                                {blog.readTime}
                              </span>
                              <span className="flex items-center gap-1">
                                <Eye className="w-3 h-3" />
                                {blog.views}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </Card>
                  </Link>
                </motion.div>
              ))}
            </div>
          </div>

          {/* Right Sidebar - Hidden on mobile */}
          {!isMobile && (
            <div className="lg:col-span-1">
            <div className="space-y-6">
              {/* Top Voices */}
              <div className="bg-card rounded-lg border p-6">
                <div className="flex items-center gap-2 mb-4">
                  <TrendingUp className="w-4 h-4 text-muted-foreground" />
                  <span className="font-medium">Top Voices</span>
                </div>
                <div className="space-y-4">
                  {categoryData.featuredExperts.map((expert, index) => (
                    <div key={expert.name} className="flex items-center gap-3">
                      <Avatar className="w-10 h-10">
                        <AvatarImage src={expert.avatar} />
                        <AvatarFallback className="text-xs">
                          {expert.name.split(' ').map(n => n[0]).join('')}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex-1 min-w-0">
                        <p className="font-medium text-gray-900 text-sm">{expert.name}</p>
                        <p className="text-xs text-gray-500 truncate">{expert.role}</p>
                        <p className="text-xs text-gray-400">{expert.expertise}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Free Guide CTA */}
              <div className="bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg p-6 text-white">
                <h3 className="font-bold text-lg mb-2">Free AI Development Guide</h3>
                <p className="text-blue-100 text-sm mb-4">
                  Get our comprehensive guide to building AI applications with modern frameworks and tools.
                </p>
                <Button className="w-full bg-white text-blue-600 hover:bg-blue-50">
                  Download Guide
                </Button>
              </div>
            </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
