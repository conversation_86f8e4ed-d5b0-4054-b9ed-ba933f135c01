'use client';

import { useState, useEffect, useCallback } from 'react';
import { useSearchPara<PERSON>, useRouter } from 'next/navigation';
import { Search, ArrowLeft, X, Filter, SlidersHorizontal, ArrowUpDown } from 'lucide-react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { <PERSON><PERSON>, <PERSON>et<PERSON>ontent, SheetHeader, SheetTitle, SheetTrigger } from '@/components/ui/sheet';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useIsMobile } from '@/components/ui/use-mobile';
import { BLOGS } from '@/constants/blogs';
import { categories } from '@/constants/categories';
import { SearchFilters } from '@/components/sections/search/SearchFilters';
import { SearchResults } from '@/components/sections/search/SearchResults';
import { SearchQuickFilters } from '@/components/sections/search/SearchQuickFilters';

export function SearchPageClient() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const isMobile = useIsMobile();

  // Initialize state from URL parameters
  const [searchQuery, setSearchQuery] = useState(searchParams.get('q') || '');
  const [filters, setFilters] = useState({
    categories: searchParams.get('categories')?.split(',').filter(Boolean) || [],
    authors: searchParams.get('authors')?.split(',').filter(Boolean) || [],
    tags: searchParams.get('tags')?.split(',').filter(Boolean) || [],
    dateRange: searchParams.get('date') || 'all',
    format: searchParams.get('format') || 'all',
    readTimeRange: searchParams.get('readTime')?.split(',').map(Number) || [0, 30],
    sortBy: searchParams.get('sort') || 'relevance'
  });
  const [filteredBlogs, setFilteredBlogs] = useState(BLOGS);
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [isMobileFilterOpen, setIsMobileFilterOpen] = useState(false);

  // Sort options for mobile
  const sortOptions = [
    { value: 'relevance', label: 'Most Relevant' },
    { value: 'latest', label: 'Latest First' },
    { value: 'oldest', label: 'Oldest First' },
    { value: 'popular', label: 'Most Popular' },
    { value: 'shortest', label: 'Quick Reads' },
    { value: 'longest', label: 'Deep Dives' },
  ];

  // Update URL when filters change
  const updateURL = useCallback((newQuery, newFilters) => {
    const params = new URLSearchParams();

    if (newQuery) params.set('q', newQuery);
    if (newFilters.categories.length > 0) params.set('categories', newFilters.categories.join(','));
    if (newFilters.authors.length > 0) params.set('authors', newFilters.authors.join(','));
    if (newFilters.tags.length > 0) params.set('tags', newFilters.tags.join(','));
    if (newFilters.dateRange !== 'all') params.set('date', newFilters.dateRange);
    if (newFilters.format !== 'all') params.set('format', newFilters.format);
    if (newFilters.readTimeRange[0] !== 0 || newFilters.readTimeRange[1] !== 30) {
      params.set('readTime', newFilters.readTimeRange.join(','));
    }
    if (newFilters.sortBy !== 'relevance') params.set('sort', newFilters.sortBy);

    const newURL = params.toString() ? `/search?${params.toString()}` : '/search';
    router.replace(newURL, { scroll: false });
  }, [router]);

  // Filter blogs based on search query and filters
  const filterBlogs = useCallback((query, currentFilters) => {
    let filtered = [...BLOGS];

    // Apply search query
    if (query.trim()) {
      const searchTerms = query.toLowerCase().split(' ').filter(term => term.length > 0);
      filtered = filtered.filter(blog => {
        const searchableText = [
          blog.title,
          blog.description,
          blog.category,
          `${blog.author.first_name} ${blog.author.last_name}`,
          ...(blog.tags || [])
        ].join(' ').toLowerCase();

        return searchTerms.every(term => searchableText.includes(term));
      });
    }

    // Apply category filters
    if (currentFilters.categories.length > 0) {
      filtered = filtered.filter(blog => currentFilters.categories.includes(blog.category));
    }

    // Apply author filters
    if (currentFilters.authors.length > 0) {
      filtered = filtered.filter(blog => {
        const authorName = `${blog.author.first_name} ${blog.author.last_name}`;
        return currentFilters.authors.includes(authorName);
      });
    }

    // Apply reading time filter
    if (currentFilters.readTimeRange[0] > 0 || currentFilters.readTimeRange[1] < 30) {
      filtered = filtered.filter(blog => {
        const readTime = parseInt(blog.readTime.replace(' min read', ''));
        return readTime >= currentFilters.readTimeRange[0] && readTime <= currentFilters.readTimeRange[1];
      });
    }

    // Apply date range filter
    if (currentFilters.dateRange !== 'all') {
      const now = new Date();
      const filterDate = new Date();

      switch (currentFilters.dateRange) {
        case 'today':
          filterDate.setDate(now.getDate() - 1);
          break;
        case 'week':
          filterDate.setDate(now.getDate() - 7);
          break;
        case 'month':
          filterDate.setMonth(now.getMonth() - 1);
          break;
        case 'year':
          filterDate.setFullYear(now.getFullYear() - 1);
          break;
      }

      filtered = filtered.filter(blog => new Date(blog.publishedAt) >= filterDate);
    }

    // Apply sorting
    switch (currentFilters.sortBy) {
      case 'latest':
        filtered.sort((a, b) => new Date(b.publishedAt) - new Date(a.publishedAt));
        break;
      case 'oldest':
        filtered.sort((a, b) => new Date(a.publishedAt) - new Date(b.publishedAt));
        break;
      case 'popular':
        filtered.sort((a, b) => parseFloat(b.views) - parseFloat(a.views));
        break;
      case 'shortest':
        filtered.sort((a, b) => {
          const aTime = parseInt(a.readTime.replace(' min read', ''));
          const bTime = parseInt(b.readTime.replace(' min read', ''));
          return aTime - bTime;
        });
        break;
      case 'longest':
        filtered.sort((a, b) => {
          const aTime = parseInt(a.readTime.replace(' min read', ''));
          const bTime = parseInt(b.readTime.replace(' min read', ''));
          return bTime - aTime;
        });
        break;
      default: // relevance
        // Keep original order for relevance
        break;
    }

    return filtered;
  }, []);

  // Handle search query change
  const handleSearchChange = (newQuery) => {
    setSearchQuery(newQuery);
    const newFiltered = filterBlogs(newQuery, filters);
    setFilteredBlogs(newFiltered);
    updateURL(newQuery, filters);
  };

  // Handle filter change
  const handleFilterChange = (newFilters) => {
    setFilters(newFilters);
    const newFiltered = filterBlogs(searchQuery, newFilters);
    setFilteredBlogs(newFiltered);
    updateURL(searchQuery, newFilters);
  };

  // Handle sort change
  const handleSortChange = (newSort) => {
    const newFilters = { ...filters, sortBy: newSort };
    handleFilterChange(newFilters);
  };

  // Clear all filters
  const clearAllFilters = () => {
    const clearedFilters = {
      categories: [],
      authors: [],
      tags: [],
      dateRange: 'all',
      format: 'all',
      readTimeRange: [0, 30],
      sortBy: 'relevance'
    };
    setFilters(clearedFilters);
    setSearchQuery('');
    setFilteredBlogs(BLOGS);
    router.replace('/search', { scroll: false });
  };

  // Initial filter application
  useEffect(() => {
    const newFiltered = filterBlogs(searchQuery, filters);
    setFilteredBlogs(newFiltered);
  }, []);

  const activeFiltersCount = filters.categories.length + filters.authors.length + filters.tags.length +
    (searchQuery ? 1 : 0) + (filters.dateRange !== 'all' ? 1 : 0) +
    (filters.readTimeRange[0] > 0 || filters.readTimeRange[1] < 30 ? 1 : 0);

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="border-b bg-background/95 backdrop-blur-sm sticky top-16 md:top-20 z-40">
        <div className="container-modern py-4 md:py-6">
          <div className="flex items-center gap-3 mb-4 md:mb-6">
            <Link href="/home" className="flex items-center text-muted-foreground hover:text-foreground transition-colors">
              <ArrowLeft className="w-4 h-4 mr-2" />
              <span className="hidden sm:inline">Back to Home</span>
              <span className="sm:hidden">Back</span>
            </Link>
          </div>

          {/* Search Header */}
          <div className="space-y-4">
            <div>
              <h1 className="text-2xl md:text-3xl font-bold mb-2">
                {searchQuery ? `Search results for "${searchQuery}"` : 'Search Articles'}
              </h1>
              <p className="text-muted-foreground text-sm md:text-base">
                {searchQuery ? `Found ${filteredBlogs.length} results` : 'Discover articles across all categories'}
              </p>
            </div>

            {/* Search Bar */}
            <div className="relative max-w-2xl">
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-muted-foreground" />
              <Input
                placeholder="Search articles, topics, or authors..."
                value={searchQuery}
                onChange={(e) => handleSearchChange(e.target.value)}
                className="pl-12 pr-4 py-3 text-sm md:text-base bg-background border-2 border-border focus:border-primary/50 rounded-xl"
              />
              {searchQuery && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="absolute right-2 top-1/2 transform -translate-y-1/2 p-1 h-8 w-8"
                  onClick={() => handleSearchChange('')}
                >
                  <X className="w-4 h-4" />
                </Button>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Quick Filters - Hidden on mobile */}
      <div className="hidden md:block">
        <SearchQuickFilters
          filters={filters}
          onFilterChange={handleFilterChange}
          searchQuery={searchQuery}
          onSearchChange={handleSearchChange}
          activeFiltersCount={activeFiltersCount}
          onClearAll={clearAllFilters}
        />
      </div>

      {/* Mobile Filter/Sort Bar */}
      {isMobile && (
        <div className="border-b bg-background/95 backdrop-blur-sm sticky top-[100px] z-30">
          <div className="container-modern py-3">
            <div className="flex items-center justify-between gap-3">
              <div className="flex items-center gap-2">
                <Sheet open={isMobileFilterOpen} onOpenChange={setIsMobileFilterOpen}>
                  <SheetTrigger asChild>
                    <Button variant="outline" size="sm" className="flex items-center gap-2">
                      <Filter className="w-4 h-4" />
                      <span>Filter</span>
                      {activeFiltersCount > 0 && (
                        <span className="bg-primary text-primary-foreground text-xs rounded-full w-5 h-5 flex items-center justify-center">
                          {activeFiltersCount}
                        </span>
                      )}
                    </Button>
                  </SheetTrigger>
                  <SheetContent side="left" className="w-full sm:w-80 p-0 flex flex-col">
                    <SheetHeader className="p-4 border-b">
                      <SheetTitle className="flex items-center gap-2">
                        <SlidersHorizontal className="w-5 h-5" />
                        Filters
                      </SheetTitle>
                    </SheetHeader>
                    <div className="flex-1 overflow-y-auto p-4">
                      <SearchFilters
                        filters={filters}
                        onFilterChange={handleFilterChange}
                        isOpen={true}
                        onToggle={() => {}}
                        isMobile={true}
                      />
                    </div>
                    <div className="p-4 border-t bg-background">
                      <div className="flex gap-2">
                        <Button
                          variant="outline"
                          className="flex-1"
                          onClick={() => setIsMobileFilterOpen(false)}
                        >
                          Close
                        </Button>
                        <Button
                          className="flex-1"
                          onClick={() => setIsMobileFilterOpen(false)}
                        >
                          Show {filteredBlogs.length} Result{filteredBlogs.length !== 1 ? 's' : ''}
                        </Button>
                      </div>
                    </div>
                  </SheetContent>
                </Sheet>

                {/* Sort Button */}
                <Select value={filters.sortBy} onValueChange={handleSortChange}>
                  <SelectTrigger asChild>
                    <Button variant="outline" size="sm" className="flex items-center gap-2">
                      <ArrowUpDown className="w-4 h-4" />
                      <span className="hidden sm:inline">Sort</span>
                    </Button>
                  </SelectTrigger>
                  <SelectContent>
                    {sortOptions.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="text-sm text-muted-foreground">
                {filteredBlogs.length} result{filteredBlogs.length !== 1 ? 's' : ''}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Mobile Quick Filters */}
      {isMobile && (
        <div className="border-b bg-muted/30">
          <div className="container-modern py-3">
            <div className="flex gap-2 overflow-x-auto scrollbar-hide">
              {categories.slice(0, 5).map((category) => (
                <Button
                  key={category}
                  variant={filters.categories.includes(category) ? "default" : "outline"}
                  size="sm"
                  onClick={() => {
                    const isSelected = filters.categories.includes(category);
                    const newCategories = isSelected
                      ? filters.categories.filter(c => c !== category)
                      : [...filters.categories, category];

                    handleFilterChange({
                      ...filters,
                      categories: newCategories
                    });
                  }}
                  className="text-xs h-8 whitespace-nowrap flex-shrink-0"
                >
                  {category}
                </Button>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Main Content */}
      <div className="container-modern py-4 md:py-6">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6 md:gap-8">
          {/* Filters Sidebar - Hidden on mobile */}
          {!isMobile && (
            <div className="lg:col-span-1">
              <div className="lg:sticky lg:top-32">
                <SearchFilters
                  filters={filters}
                  onFilterChange={handleFilterChange}
                  isOpen={isFilterOpen}
                  onToggle={() => setIsFilterOpen(!isFilterOpen)}
                />
              </div>
            </div>
          )}

          {/* Search Results */}
          <div className={isMobile ? "col-span-1" : "lg:col-span-3"}>
            <SearchResults
              blogs={filteredBlogs}
              searchQuery={searchQuery}
              filters={filters}
              onFilterChange={handleFilterChange}
              isMobile={isMobile}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
