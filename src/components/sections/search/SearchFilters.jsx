'use client';

import { useState } from 'react';
import { motion, AnimatePresence } from 'motion/react';
import { Filter, ChevronDown, ChevronUp, X, User, Calendar, Clock, Tag, FileText } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { Checkbox } from '@/components/ui/checkbox';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { categories } from '@/constants/categories';
import { BLOGS } from '@/constants/blogs';

export function SearchFilters({ filters, onFilterChange, isOpen, onToggle }) {
  const [expandedSections, setExpandedSections] = useState({
    categories: true,
    authors: true,
    date: true,
    readTime: true,
    tags: false
  });

  // Get unique authors from all blogs
  const allAuthors = [...new Set(BLOGS.map(blog => `${blog.author.first_name} ${blog.author.last_name}`))];
  
  // Get all available tags
  const allTags = [
    'Machine Learning', 'Startup', 'Remote Work', 'Cryptocurrency',
    'Web Development', 'Mobile Apps', 'Data Science', 'UX Design',
    'Marketing', 'Productivity', 'Health Tech', 'Fintech', 'AI Ethics',
    'Blockchain', 'Cloud Computing', 'Cybersecurity', 'DevOps', 'IoT'
  ];

  const dateOptions = [
    { value: 'all', label: 'All Time' },
    { value: 'today', label: 'Today' },
    { value: 'week', label: 'This Week' },
    { value: 'month', label: 'This Month' },
    { value: 'year', label: 'This Year' },
  ];

  const formatOptions = [
    { value: 'all', label: 'All Formats' },
    { value: 'article', label: 'Article' },
    { value: 'tutorial', label: 'Tutorial' },
    { value: 'guide', label: 'Guide' },
    { value: 'review', label: 'Review' },
  ];

  const toggleSection = (section) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  const handleCategoryChange = (category, checked) => {
    const newCategories = checked
      ? [...filters.categories, category]
      : filters.categories.filter(c => c !== category);
    
    onFilterChange({
      ...filters,
      categories: newCategories
    });
  };

  const handleAuthorChange = (author, checked) => {
    const newAuthors = checked
      ? [...filters.authors, author]
      : filters.authors.filter(a => a !== author);
    
    onFilterChange({
      ...filters,
      authors: newAuthors
    });
  };

  const handleTagChange = (tag, checked) => {
    const newTags = checked
      ? [...filters.tags, tag]
      : filters.tags.filter(t => t !== tag);
    
    onFilterChange({
      ...filters,
      tags: newTags
    });
  };

  const handleDateChange = (newDate) => {
    onFilterChange({
      ...filters,
      dateRange: newDate
    });
  };

  const handleFormatChange = (newFormat) => {
    onFilterChange({
      ...filters,
      format: newFormat
    });
  };

  const handleReadTimeChange = (newRange) => {
    onFilterChange({
      ...filters,
      readTimeRange: newRange
    });
  };

  const clearAllFilters = () => {
    onFilterChange({
      categories: [],
      authors: [],
      tags: [],
      dateRange: 'all',
      format: 'all',
      readTimeRange: [0, 30],
      sortBy: 'relevance'
    });
  };

  const activeFiltersCount = filters.categories.length + filters.authors.length + filters.tags.length +
    (filters.dateRange !== 'all' ? 1 : 0) + (filters.format !== 'all' ? 1 : 0) +
    (filters.readTimeRange[0] > 0 || filters.readTimeRange[1] < 30 ? 1 : 0);

  const FilterSection = ({ title, icon: Icon, section, children }) => (
    <div className="border-b border-border/50 last:border-b-0">
      <button
        onClick={() => toggleSection(section)}
        className="w-full flex items-center justify-between p-4 hover:bg-muted/50 transition-colors"
      >
        <div className="flex items-center gap-2">
          <Icon className="w-4 h-4 text-muted-foreground" />
          <span className="font-medium text-sm">{title}</span>
        </div>
        {expandedSections[section] ? (
          <ChevronUp className="w-4 h-4 text-muted-foreground" />
        ) : (
          <ChevronDown className="w-4 h-4 text-muted-foreground" />
        )}
      </button>
      <AnimatePresence>
        {expandedSections[section] && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.2 }}
            className="overflow-hidden"
          >
            <div className="p-4 pt-0">
              {children}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );

  return (
    <Card className="sticky top-32">
      <CardHeader className="pb-4">
        <CardTitle className="flex items-center justify-between text-base">
          <div className="flex items-center gap-2">
            <Filter className="w-4 h-4" />
            Filters
            {activeFiltersCount > 0 && (
              <Badge variant="secondary" className="ml-2 px-1.5 py-0.5 text-xs">
                {activeFiltersCount}
              </Badge>
            )}
          </div>
          {activeFiltersCount > 0 && (
            <Button variant="ghost" size="sm" onClick={clearAllFilters} className="text-xs">
              Clear All
            </Button>
          )}
        </CardTitle>
      </CardHeader>
      
      <CardContent className="p-0">
        {/* Categories */}
        <FilterSection title="Categories" icon={Tag} section="categories">
          <div className="space-y-3">
            {categories.map((category) => (
              <div key={category} className="flex items-center space-x-2">
                <Checkbox
                  id={`category-${category}`}
                  checked={filters.categories.includes(category)}
                  onCheckedChange={(checked) => handleCategoryChange(category, checked)}
                />
                <label
                  htmlFor={`category-${category}`}
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
                >
                  {category}
                </label>
              </div>
            ))}
          </div>
        </FilterSection>

        {/* Authors */}
        <FilterSection title="Authors" icon={User} section="authors">
          <div className="space-y-3 max-h-48 overflow-y-auto">
            {allAuthors.slice(0, 10).map((author) => (
              <div key={author} className="flex items-center space-x-2">
                <Checkbox
                  id={`author-${author}`}
                  checked={filters.authors.includes(author)}
                  onCheckedChange={(checked) => handleAuthorChange(author, checked)}
                />
                <label
                  htmlFor={`author-${author}`}
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
                >
                  {author}
                </label>
              </div>
            ))}
          </div>
        </FilterSection>

        {/* Date Range */}
        <FilterSection title="Date" icon={Calendar} section="date">
          <Select value={filters.dateRange} onValueChange={handleDateChange}>
            <SelectTrigger className="w-full">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {dateOptions.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </FilterSection>

        {/* Reading Time */}
        <FilterSection title="Reading Time" icon={Clock} section="readTime">
          <div className="space-y-4">
            <div className="text-sm text-muted-foreground">
              {filters.readTimeRange[0]}-{filters.readTimeRange[1]} minutes
            </div>
            <Slider
              value={filters.readTimeRange}
              onValueChange={handleReadTimeChange}
              max={30}
              min={0}
              step={1}
              className="w-full"
            />
          </div>
        </FilterSection>

        {/* Format */}
        <FilterSection title="Format" icon={FileText} section="format">
          <Select value={filters.format} onValueChange={handleFormatChange}>
            <SelectTrigger className="w-full">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {formatOptions.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </FilterSection>

        {/* Tags */}
        <FilterSection title="Tags" icon={Tag} section="tags">
          <div className="space-y-3 max-h-48 overflow-y-auto">
            {allTags.slice(0, 12).map((tag) => (
              <div key={tag} className="flex items-center space-x-2">
                <Checkbox
                  id={`tag-${tag}`}
                  checked={filters.tags.includes(tag)}
                  onCheckedChange={(checked) => handleTagChange(tag, checked)}
                />
                <label
                  htmlFor={`tag-${tag}`}
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
                >
                  {tag}
                </label>
              </div>
            ))}
          </div>
        </FilterSection>
      </CardContent>
    </Card>
  );
}
