'use client';

import { motion } from 'motion/react';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Clock, Eye, Calendar, TrendingUp, ArrowUpRight } from 'lucide-react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';

const sortOptions = [
  { value: 'relevance', label: 'Most Relevant', icon: TrendingUp },
  { value: 'latest', label: 'Latest First', icon: Calendar },
  { value: 'oldest', label: 'Oldest First', icon: Calendar },
  { value: 'popular', label: 'Most Popular', icon: TrendingUp },
  { value: 'shortest', label: 'Quick Reads', icon: Clock },
  { value: 'longest', label: 'Deep Dives', icon: Clock },
];

export function SearchResults({ blogs, searchQuery, filters, onFilterChange, isMobile = false }) {
  const handleSortChange = (newSort) => {
    onFilterChange({
      ...filters,
      sortBy: newSort
    });
  };

  const highlightText = (text, query) => {
    if (!query.trim()) return text;
    
    const searchTerms = query.toLowerCase().split(' ').filter(term => term.length > 0);
    let highlightedText = text;
    
    searchTerms.forEach(term => {
      const regex = new RegExp(`(${term})`, 'gi');
      highlightedText = highlightedText.replace(regex, '<mark class="bg-yellow-200 dark:bg-yellow-800 px-1 rounded">$1</mark>');
    });
    
    return highlightedText;
  };

  const getMatchPercentage = (blog) => {
    if (!searchQuery.trim()) return null;
    
    const searchTerms = searchQuery.toLowerCase().split(' ').filter(term => term.length > 0);
    const searchableText = [
      blog.title,
      blog.description,
      blog.category,
      `${blog.author.first_name} ${blog.author.last_name}`
    ].join(' ').toLowerCase();
    
    const matches = searchTerms.filter(term => searchableText.includes(term));
    return Math.round((matches.length / searchTerms.length) * 100);
  };

  if (blogs.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="max-w-md mx-auto">
          <div className="w-24 h-24 mx-auto mb-6 bg-muted rounded-full flex items-center justify-center">
            <TrendingUp className="w-12 h-12 text-muted-foreground" />
          </div>
          <h3 className="text-xl font-semibold mb-2">No articles found</h3>
          <p className="text-muted-foreground mb-6">
            {searchQuery 
              ? `No articles match your search for "${searchQuery}". Try different keywords or adjust your filters.`
              : 'No articles match your current filters. Try adjusting your search criteria.'
            }
          </p>
          <Button variant="outline" onClick={() => window.location.href = '/search'}>
            Clear Search
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Results Header */}
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div>
          <h2 className="text-lg font-semibold">
            {blogs.length} article{blogs.length !== 1 ? 's' : ''} found
          </h2>
          {searchQuery && (
            <p className="text-sm text-muted-foreground mt-1">
              Showing results for "{searchQuery}"
            </p>
          )}
        </div>

        {/* Sort Dropdown */}
        <Select value={filters.sortBy} onValueChange={handleSortChange}>
          <SelectTrigger className={isMobile ? "w-full" : "w-48"}>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {sortOptions.map((option) => (
              <SelectItem key={option.value} value={option.value}>
                <div className="flex items-center gap-2">
                  <option.icon className="w-4 h-4" />
                  {option.label}
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Results List */}
      <div className="space-y-4">
        {blogs.map((blog, index) => {
          const matchPercentage = getMatchPercentage(blog);
          
          return (
            <motion.div
              key={blog.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.05 }}
            >
              <Link href={`/blog/${blog.slug || blog.id}`}>
                <Card className="overflow-hidden hover:shadow-lg transition-all duration-300 cursor-pointer group border-l-4 border-l-transparent hover:border-l-primary">
                  <div className={isMobile ? "flex flex-col" : "flex"}>
                    {/* Image */}
                    <div className={`${isMobile ? "w-full h-48" : "w-48 h-32 flex-shrink-0"} relative overflow-hidden`}>
                      <img
                        src={blog.image}
                        alt={blog.title}
                        className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                    </div>

                    {/* Content */}
                    <div className={isMobile ? "p-4" : "flex-1 p-6"}>
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex items-center gap-2">
                          <Badge variant="secondary" className="text-xs">
                            {blog.category}
                          </Badge>
                          {matchPercentage && (
                            <Badge variant="outline" className="text-xs text-green-600 border-green-200">
                              {matchPercentage}% match
                            </Badge>
                          )}
                        </div>
                        <Button variant="ghost" size="sm" className="opacity-0 group-hover:opacity-100 transition-opacity">
                          <ArrowUpRight className="w-4 h-4" />
                        </Button>
                      </div>

                      <h3
                        className={`${isMobile ? 'text-base' : 'text-lg'} font-semibold text-foreground mb-2 group-hover:text-primary transition-colors line-clamp-2`}
                        dangerouslySetInnerHTML={{ __html: highlightText(blog.title, searchQuery) }}
                      />

                      <p
                        className={`text-muted-foreground text-sm mb-4 ${isMobile ? 'line-clamp-3' : 'line-clamp-2'}`}
                        dangerouslySetInnerHTML={{ __html: highlightText(blog.description, searchQuery) }}
                      />

                      {/* Tags */}
                      {blog.tags && blog.tags.length > 0 && (
                        <div className="flex flex-wrap gap-1 mb-4">
                          {blog.tags.slice(0, 3).map((tag) => (
                            <span 
                              key={tag} 
                              className="px-2 py-1 bg-primary/10 text-primary text-xs rounded-md"
                              dangerouslySetInnerHTML={{ __html: highlightText(tag, searchQuery) }}
                            />
                          ))}
                          {blog.tags.length > 3 && (
                            <span className="px-2 py-1 bg-muted text-muted-foreground text-xs rounded-md">
                              +{blog.tags.length - 3} more
                            </span>
                          )}
                        </div>
                      )}

                      {/* Meta Information */}
                      <div className={isMobile ? "flex flex-col gap-3" : "flex items-center justify-between"}>
                        <div className={isMobile ? "flex items-center gap-3" : "flex items-center gap-4"}>
                          <div className="flex items-center gap-2">
                            <Avatar className="w-6 h-6">
                              <AvatarImage src={blog.author.avatar} />
                              <AvatarFallback className="text-xs">
                                {blog.author.first_name[0]}{blog.author.last_name[0]}
                              </AvatarFallback>
                            </Avatar>
                            <span
                              className="text-sm text-muted-foreground"
                              dangerouslySetInnerHTML={{
                                __html: highlightText(`${blog.author.first_name} ${blog.author.last_name}`, searchQuery)
                              }}
                            />
                          </div>
                          {!isMobile && <span className="text-muted-foreground">•</span>}
                          <span className="text-sm text-muted-foreground">{blog.publishedAt}</span>
                        </div>

                        <div className={`flex items-center gap-3 text-sm text-muted-foreground ${isMobile ? 'justify-start' : 'gap-4'}`}>
                          <span className="flex items-center gap-1">
                            <Clock className="w-3 h-3" />
                            {blog.readTime}
                          </span>
                          <span className="flex items-center gap-1">
                            <Eye className="w-3 h-3" />
                            {blog.views}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </Card>
              </Link>
            </motion.div>
          );
        })}
      </div>

      {/* Load More Button (if needed) */}
      {blogs.length > 10 && (
        <div className="text-center pt-8">
          <Button variant="outline" size="lg">
            Load More Results
          </Button>
        </div>
      )}
    </div>
  );
}
